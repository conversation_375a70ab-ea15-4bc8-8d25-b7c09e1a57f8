#!/usr/bin/env python3
"""
Script principal para extração de componentes do Figma.

Este script orquestra a extração de componentes do Figma com interface simplificada
para entrada de node ID ou URL, gerando JSONs estruturados normalizados.
"""

import argparse
import re
import sys
from pathlib import Path
from typing import List, Optional
from urllib.parse import urlparse

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.figma.figma_api_client import FigmaApiClient, FigmaApiConfig
from src.figma.figma_discovery import FigmaDiscovery, DiscoveredNode, NodeType
from src.figma.figma_normalized_extractor import FigmaNormalizedExtractor
from src.utils.config import load_project_config
from src.utils.file.file_utils import node_id_pattern
from src.utils.logging import setup_script_logging

# Setup logging será configurado na função main
logger = None


class FigmaExtractor:
    """
    Extrator de componentes do Figma com interface simplificada.
    """
    
    def __init__(self, config_loader):
        """
        Inicializa o extrator.
        """
        self.config_loader = config_loader
        self.config = config_loader.load_config()
        
        # Obter configurações do Figma
        figma_config = config_loader.get_figma_config()
        self.file_key = figma_config['file_key']
        self.token = figma_config['token']
        self.max_exploration_depth = figma_config.get('max_exploration_depth', 5)
        
        # Obter configurações de saída
        output_config = config_loader.get_output_config()
        self.output_config = output_config
        
        self.file_info = None
        # Histórico de navegação para suportar "voltar"
        self.navigation_history = []
        self._initialize_components()
    
    def _initialize_components(self):
        """Inicializa os componentes necessários."""
        try:
            api_config = FigmaApiConfig(token=self.token)
            self.figma_client = FigmaApiClient(api_config)
            self.discovery = FigmaDiscovery(self.token)
            self.normalized_extractor = FigmaNormalizedExtractor()
            
            # Obter informações do arquivo uma vez
            self.file_info = self.discovery.get_file_info(self.file_key)
            logger.debug(f"Trabalhando com o arquivo: {self.file_info.get('name')}")

        except Exception as e:
            logger.error(f"Erro na inicialização: {e}", exc_info=True)
            raise
    
    def extract_node_id_from_url(self, url: str) -> Optional[str]:
        """
        Extrai o node ID de uma URL do Figma.
        
        Args:
            url: URL do Figma
            
        Returns:
            Node ID extraído ou None se não encontrado
        """
        try:
            # Padrão para extrair node-id da URL (ex: 123:456 ou 123-456)
            match = re.search(r'node-id=([0-9%A-F:-]+)', url)
            if match:
                return match.group(1).replace('%3A', ':')

            # Se não encontrar, tentar extrair da URL diretamente
            parsed_url = urlparse(url)
            path_parts = parsed_url.path.split('/')
            
            # Procurar por padrões de node ID na URL
            for part in path_parts:
                if self.validate_node_id(part):
                    return part
            
            return None
        except Exception as e:
            logger.warning(f"Erro ao extrair node ID da URL: {e}")
            return None
    
    def validate_node_id(self, node_id: str) -> bool:
        """
        Valida se o node ID tem formato correto.
        
        Args:
            node_id: ID do node para validar
            
        Returns:
            True se válido, False caso contrário
        """
        return node_id_pattern.match(node_id) is not None

    def extract_interactive(self) -> bool:
        """
        Executa a extração interativa com interface simplificada.
        
        Returns:
            bool: True se a extração foi bem-sucedida, False caso contrário
        """
        logger.debug(f"Iniciando extração do arquivo: {self.file_key}")
        
        try:
            print("\n" + "="*80)
            print("🎯 EXTRATOR FIGMA")
            print("="*80)
            print("Digite o Node ID ou URL do Figma para extrair:")
            print("="*80)
            
            user_input = input("\n👉 Digite o Node ID ou URL: ").strip()
            
            if not user_input:
                print("❌ Entrada vazia. Encerrando.")
                return False
            
            # Extrair node ID da entrada
            node_id = None
            
            # Verificar se é uma URL
            if user_input.startswith('http'):
                node_id = self.extract_node_id_from_url(user_input)
                if node_id:
                    print(f"✅ Node ID extraído da URL: {node_id}")
                else:
                    print("❌ Não foi possível extrair Node ID da URL fornecida.")
                    return False
            else:
                # Verificar se é um node ID válido
                if self.validate_node_id(user_input):
                    node_id = user_input
                else:
                    print("❌ Formato de Node ID inválido. Use o formato: número-número (ex: 13-7453)")
                    return False
            
            # Buscar o node
            print(f"🔍 Buscando node com ID: {node_id}...")
            node_data = self.figma_client.get_file_nodes(self.file_key, [node_id])
            
            if not node_data or not node_data.get('nodes'):
                print(f"❌ Nenhum node encontrado com o ID: {node_id}")
                return False
            
            # Processar o node encontrado
            node_info = list(node_data['nodes'].values())[0]
            
            # Verificar se node_info é None (node inválido)
            if node_info is None:
                print(f"❌ Node inválido ou não encontrado com o ID: {node_id}")
                return False
            
            document = node_info.get('document', {})
            
            discovered_node = DiscoveredNode(
                id=document.get('id'),
                name=document.get('name'),
                type=document.get('type'),
                node_type=NodeType.from_string(document.get('type')),
                raw_data=document,
                level=0
            )
            
            print(f"\n✅ Node encontrado: {discovered_node.name} ({discovered_node.type})")
            logger.debug(f"✅ Node encontrado: {discovered_node.name} ({discovered_node.type})")
            
            # Processar o node
            self._handle_selected_nodes([discovered_node], parent_name="DirectNode", current_level=1)
            return True

        except Exception as e:
            logger.error(f"Erro na extração interativa: {e}", exc_info=True)
            return False

    def _handle_selected_nodes(self, nodes: List[DiscoveredNode], parent_name: str, current_level: int = 1):
        """
        Lida com nodes selecionados, seja para explorar ou extrair.
        Esta função unifica a lógica que antes estava no final de _explore_nodes_recursively.
        
        Args:
            nodes: Lista de nodes selecionados
            parent_name: Nome do elemento pai
            current_level: Nível atual de exploração (1 = primeiro nível)
        """
        if not nodes:
            return

        # Verificar se algum node é uma ação de voltar
        back_nodes = [node for node in nodes if node.raw_data.get('is_back_action', False)]
        if back_nodes:
            self._handle_back_action(current_level)
            return

        # Decide se continua explorando ou extrai
        can_explore_further = any(child.type in ['SECTION', 'FRAME', 'INSTANCE', 'COMPONENT_SET'] for child in nodes)
        
        # Verifica se atingiu a profundidade máxima
        reached_max_depth = current_level >= self.max_exploration_depth

        action = '1' # Padrão para extrair
        if can_explore_further and not reached_max_depth:
            print(f"\n💡 Ação para os itens selecionados (Nível {current_level}/{self.max_exploration_depth}):")
            print("   1. Extrair (salva os itens selecionados como componentes)")
            print("   2. Explorar (continua a navegação para o próximo nível)")
            print("   3. Voltar (retorna ao nível anterior)")

            while True:
                choice = input("👉 Escolha a ação (1, 2 ou 3): ").strip()
                if choice in ['1', '2', '3']:
                    action = choice
                    break
                print("Opção inválida. Tente novamente.")
        elif reached_max_depth:
            print(f"\n⚠️  Atingiu a profundidade máxima de exploração ({self.max_exploration_depth} níveis)")
            print("   Extraindo componentes automaticamente...")
            action = '1'
        else:
            print(f"\n✨ Extraindo {len(nodes)} componente(s) (nível final)...")
            action = '1'
        
        if action == '1':
            print(f"\n✨ Extraindo {len(nodes)} componente(s)...")
            self._extract_and_save_from_nodes(nodes, parent_name=parent_name)
        elif action == '2':
            # Explorar filhos dos nodes selecionados
            self._explore_children_of_nodes(nodes, current_level + 1)
        elif action == '3':
            # Ação de voltar
            self._handle_back_action(current_level)

    def _handle_back_action(self, current_level: int):
        """
        Trata a ação de voltar ao nível anterior.
        
        Args:
            current_level: Nível atual de exploração
        """
        if current_level <= 1:
            print("⚠️  Já está no nível mais alto. Não é possível voltar mais.")
            return
        
        if not self.navigation_history:
            print("⚠️  Não há histórico de navegação para voltar.")
            return
        
        # Remover o último item do histórico
        previous_state = self.navigation_history.pop()
        
        print(f"⬅️  Voltando do nível {current_level} para o nível {previous_state['level']}")
        print(f"   Retornando para: {previous_state['parent_name']}")
        
        # Retomar a exploração do estado anterior
        self._explore_nodes_recursively(previous_state['nodes'], level=previous_state['level'])

    def _explore_children_of_nodes(self, nodes: List[DiscoveredNode], level: int):
        """
        Explora especificamente os filhos dos nodes fornecidos.

        Args:
            nodes: Lista de nodes cujos filhos devem ser explorados
            level: Nível atual de exploração
        """
        if not nodes:
            return

        print(f"\n🔍 Coletando filhos dos {len(nodes)} node(s) selecionado(s)...")

        # Coletar todos os filhos dos nodes selecionados
        all_children = []

        for node in nodes:
            try:
                children = self.discovery.discover_node_children(self.file_key, node.id, max_depth=1)
                if children:
                    all_children.extend(children)
                    print(f"   ✅ {node.name}: {len(children)} filho(s) encontrado(s)")
                else:
                    print(f"   ⚠️  {node.name}: Sem filhos")
            except Exception as e:
                logger.warning(f"Erro ao explorar filhos de {node.name}: {e}")
                print(f"   ❌ {node.name}: Erro ao explorar")

        if not all_children:
            print("\n⚠️  Nenhum filho encontrado nos nodes selecionados.")
            print("   Retornando para processar os nodes originais...")
            self._handle_selected_nodes(nodes, parent_name=f"Level{level-1}", current_level=level-1)
            return

        print(f"\n✅ Total de {len(all_children)} filho(s) encontrado(s). Iniciando exploração...")

        # Continuar a exploração recursiva com os filhos encontrados
        self._explore_nodes_recursively(all_children, level)

    def _explore_nodes_recursively(self, nodes: List[DiscoveredNode], level: int):
        """
        Função recursiva para explorar a hierarquia de nodes de forma flexível.

        Args:
            nodes: Lista de nodes para explorar
            level: Nível atual de exploração
        """
        if not nodes:
            return

        # Salvar estado atual no histórico para suportar "voltar"
        if level > 1:  # Não salvar o estado inicial
            self.navigation_history.append({
                'level': level - 1,
                'nodes': nodes,
                'parent_name': nodes[0].name if nodes else "Unknown"
            })

        # Mostrar opções de navegação
        print(f"\n" + "="*80 + f"\n📂 Nível {level}: Explorando {len(nodes)} item(s)\n" + "="*80)

        # Coletar todos os filhos dos nodes atuais
        all_children = []

        # Adicionar opção de voltar se não estiver no nível inicial
        if level > 1:
            back_node = DiscoveredNode(
                id="back",
                name="⬅️ Voltar ao nível anterior",
                type="BACK",
                node_type=NodeType.GROUP,
                raw_data={'is_back_action': True},
                level=level - 1
            )
            all_children.append(back_node)

        # Explorar cada node selecionado e coletar seus filhos
        for i, node in enumerate(nodes):
            print(f"  {i+1:2d}. {node.name} ({node.type})")

            # Tentar descobrir filhos do node
            try:
                children = self.discovery.discover_node_children(self.file_key, node.id, max_depth=1)
                if children:
                    all_children.extend(children)
                    print(f"      └─ {len(children)} filho(s) encontrado(s)")
                else:
                    print(f"      └─ Sem filhos")
            except Exception as e:
                logger.warning(f"Erro ao explorar filhos de {node.name}: {e}")
                print(f"      └─ Erro ao explorar")

        # Se não há filhos para navegar, processar os nodes atuais
        if not all_children or (len(all_children) == 1 and all_children[0].raw_data.get('is_back_action', False)):
            print("\n⚠️  Nenhum filho encontrado. Processando nodes atuais...")
            self._handle_selected_nodes(nodes, parent_name=f"Level{level}", current_level=level)
            return

        # Mostrar os filhos encontrados para seleção
        print(f"\n📋 Filhos encontrados:")
        for i, child in enumerate(all_children):
            if child.raw_data.get('is_back_action', False):
                print(f"  {i+1:2d}. {child.name}")
            else:
                print(f"  {i+1:2d}. {child.name} ({child.type})")

        # Permitir seleção múltipla dos filhos
        print(f"\n👉 Selecione os filhos para explorar ou extrair (separados por vírgula):")
        print("   Exemplo: 1,3,5 para selecionar itens 1, 3 e 5")
        print("   Ou 'all' para selecionar todos")

        while True:
            choice = input("   Sua escolha: ").strip().lower()

            if choice == 'all':
                selected_nodes = all_children
                break
            elif choice == '':
                print("   Por favor, faça uma seleção.")
                continue
            else:
                try:
                    # Parse seleção múltipla
                    indices = [int(x.strip()) - 1 for x in choice.split(',')]
                    selected_nodes = [all_children[i] for i in indices if 0 <= i < len(all_children)]

                    if not selected_nodes:
                        print("   Nenhum item válido selecionado. Tente novamente.")
                        continue

                    break
                except (ValueError, IndexError):
                    print("   Formato inválido. Use números separados por vírgula.")
                    continue

        # Processar nodes selecionados
        self._handle_selected_nodes(selected_nodes, parent_name=f"Level{level}", current_level=level + 1)

    def _extract_and_save_from_nodes(self, nodes: List[DiscoveredNode], parent_name: str) -> None:
        """
        Extrai e salva componentes a partir de uma lista de nodes.
        
        Args:
            nodes: Lista de nodes para extrair
            parent_name: Nome do elemento pai para organização
        """
        if not nodes:
            logger.warning("Nenhum node fornecido para extração")
            return
        
        # Obter nome do projeto/arquivo Figma
        project_name = self.file_info.get('name', 'unknown') if self.file_info else 'unknown'
        
        extracted_count = 0
        failed_count = 0
        
        for node in nodes:
            try:
                logger.debug(f"Extraindo componente: {node.name}")
                
                # Obter dados completos do node
                node_data = self.figma_client.get_file_nodes(self.file_key, [node.id])
                if not node_data or not node_data.get('nodes'):
                    logger.warning(f"Não foi possível obter dados para {node.name}")
                    failed_count += 1
                    continue
                
                # Extrair dados do node
                node_info = list(node_data['nodes'].values())[0]
                document = node_info.get('document', {})
                
                # Combinar dados do document com dados do node_info (que contém components e componentSets)
                complete_data = {**document}
                if 'components' in node_info:
                    complete_data['components'] = node_info['components']
                if 'componentSets' in node_info:
                    complete_data['componentSets'] = node_info['componentSets']
                
                # Usar extrator normalizado que gera JSONs estruturados
                component_data = self.normalized_extractor.extract_normalized_component(complete_data)
                
                if component_data:
                    # Salvar componente normalizado usando nome do projeto e node
                    self.normalized_extractor.save_normalized_component(component_data, project_name, node.name)
                    extracted_count += 1
                    logger.debug(f"✅ Componente extraído: {node.name}")
                else:
                    logger.warning(f"❌ Falha na extração: {node.name}")
                    failed_count += 1
                
            except Exception as e:
                logger.error(f"Erro ao extrair {node.name}: {e}", exc_info=True)
                failed_count += 1
        
        if extracted_count > 0:
            print(f"\n✅ Extraídos com sucesso: {extracted_count} componente(s)")
        if failed_count > 0:
            print(f"\n❌ Falhas: {failed_count} componente(s) não extraídos")

def main():
    """Função principal do script."""
    parser = argparse.ArgumentParser(description="Extrair componentes do Figma")
    parser.add_argument(
        "--config-file", 
        default="project_config.yaml",
        help="Arquivo de configuração (project_config.yaml)"
    )
    parser.add_argument(
        "--output-dir", 
        help="Diretório de saída para os arquivos extraídos"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Verbose output"
    )
    
    args = parser.parse_args()
    
    # Configurar logging personalizado para o script
    global logger
    logger = setup_script_logging("figma_extract", verbose=args.verbose)
    
    logger.debug("Iniciando extração do Figma...")
    
    try:
        # Carregar configuração
        config_loader = load_project_config(args.config_file)
        
        # Inicializar extrator
        extractor = FigmaExtractor(config_loader)
        
        # Executar extração interativa
        extraction_success = extractor.extract_interactive()
        
        if extraction_success:
            print("\n" + "="*50)
            print("EXTRAÇÃO CONCLUÍDA")
            print("="*50)
            print(f"Arquivo Figma: {extractor.file_info.get('name') if extractor.file_info else 'N/A'}")
            print(f"Diretório de saída: {extractor.normalized_extractor.output_dir}")
            print("Modo: Extração de JSONs estruturados normalizados")
            print("="*50)
        else:
            print("\n" + "="*50)
            print("NÃO FOI POSSÍVEL EFETUAR A EXTRAÇÃO")
            print("="*50)
        
    except Exception as e:
        logger.error(f"Erro na extração: {e}", exc_info=True)
        print(f"\n❌ Erro na extração: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()